name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test on ${{ matrix.os }} with ${{ matrix.compiler }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest]
        compiler: [gcc, clang]
        exclude:
          # macOS doesn't have gcc in the default environment
          - os: macos-latest
            compiler: gcc

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential \
          gcc \
          g++ \
          clang \
          llvm \
          cmake \
          lcov \
          python3-pip

    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        # Xcode command line tools are pre-installed on GitHub Actions macOS runners
        # Install Homebrew packages
        brew install llvm lcov

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov pytest-xdist pytest-html coverage

    - name: Set up environment variables
      run: |
        if [ "${{ matrix.compiler }}" = "clang" ]; then
          if [ "${{ matrix.os }}" = "macos-latest" ]; then
            echo "PATH=/opt/homebrew/opt/llvm/bin:$PATH" >> $GITHUB_ENV
          elif [ "${{ matrix.os }}" = "ubuntu-latest" ]; then
            # On Ubuntu, LLVM tools might be versioned (e.g., llvm-profdata-14)
            # Try to find the correct version and create symlinks if needed
            LLVM_VERSION=$(clang --version | grep -oP 'clang version \K[0-9]+' | head -1)
            if [ -n "$LLVM_VERSION" ]; then
              echo "LLVM_VERSION=$LLVM_VERSION" >> $GITHUB_ENV
            fi
          fi
          echo "CC=clang" >> $GITHUB_ENV
          echo "CXX=clang++" >> $GITHUB_ENV
        else
          echo "CC=gcc" >> $GITHUB_ENV
          echo "CXX=g++" >> $GITHUB_ENV
        fi

        # Set coverage environment for Clang
        if [ "${{ matrix.compiler }}" = "clang" ]; then
          echo "LLVM_PROFILE_FILE=build/coverage-%p.profraw" >> $GITHUB_ENV
        fi

    - name: Build project
      run: |
        mkdir -p build
        cd build
        cmake .. -DCMAKE_BUILD_TYPE=Release
        make

    - name: Run tests (without coverage)
      run: |
        python -m pytest tests/ -v --tb=short

    - name: Build with coverage
      run: |
        rm -rf build
        mkdir -p build
        cd build
        # Force specific compiler for coverage
        if [ "${{ matrix.compiler }}" = "gcc" ]; then
          CC=gcc CXX=g++ cmake .. -DENABLE_COVERAGE=ON -DCMAKE_BUILD_TYPE=Debug
        else
          CC=clang CXX=clang++ cmake .. -DENABLE_COVERAGE=ON -DCMAKE_BUILD_TYPE=Debug
        fi
        make

    - name: Run tests with coverage
      run: |
        # Ensure the executable path is correct for tests
        export PYDCOV_EXECUTABLE="./build/pydcov"
        python -m pytest tests/ -v --tb=short

    - name: Generate coverage report
      run: |
        cd build
        make coverage-report

    - name: Upload coverage to Codecov
      if: matrix.os == 'ubuntu-latest' && matrix.compiler == 'gcc'
      uses: codecov/codecov-action@v4
      with:
        file: build/coverage/coverage.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v4
      with:
        name: coverage-${{ matrix.os }}-${{ matrix.compiler }}
        path: |
          build/coverage/
          !build/coverage/*.profraw
        retention-days: 7

  build-docs:
    name: Build Documentation
    runs-on: ubuntu-latest
    needs: test

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential gcc g++ cmake lcov
        python -m pip install --upgrade pip
        pip install pytest pytest-cov pytest-xdist pytest-html coverage

    - name: Build project with coverage
      run: |
        mkdir -p build
        cd build
        CC=gcc CXX=g++ cmake .. -DENABLE_COVERAGE=ON -DCMAKE_BUILD_TYPE=Debug
        make

    - name: Run tests
      run: |
        python -m pytest tests/ -v

    - name: Generate coverage report
      run: |
        cd build
        make coverage-report

    - name: Deploy coverage report to GitHub Pages
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: build/coverage/html
        destination_dir: coverage

  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: [test, build-docs]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up build environment
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential gcc g++ cmake

    - name: Build release binaries
      run: |
        mkdir -p build
        cd build
        cmake .. -DCMAKE_BUILD_TYPE=Release
        make
        mkdir -p ../dist
        cp pydcov ../dist/pydcov-linux-x64
        strip ../dist/pydcov-linux-x64

    - name: Create release archive
      run: |
        cd dist
        tar -czf pydcov-linux-x64.tar.gz pydcov-linux-x64
        cd ..

    - name: Upload release artifacts
      uses: actions/upload-artifact@v4
      with:
        name: release-binaries
        path: dist/
        retention-days: 30
