# Build directories
build/
dist/

# Coverage files
*.gcda
*.gcno
*.profraw
*.profdata
coverage.info

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
/tmp/
*.log

# Dynamic array storage (used by tests)
/tmp/pydcov_array.dat
